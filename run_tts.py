import torchaudio
from speechbrain.pretrained import Tacotron2
from speechbrain.pretrained import HIFIGAN

# Load the pretrained Tacotron2 model and HiFIGAN vocoder
tacotron2 = Tacotron2.from_hparams(source="Sunbird/tts-tacotron2-lug", savedir="tmpdir_tts")
hifi_gan = HIFIGAN.from_hparams(source="speechbrain/tts-hifigan-ljspeech", savedir="tmpdir_vocoder")

# Text to be converted to speech
text_to_speak = "Nsanyuse okukulaba"

# Generate the mel spectrogram from the text
mel_output, mel_length, alignment = tacotron2.encode_text(text_to_speak)

# Use the vocoder to generate the waveform from the spectrogram
waveforms = hifi_gan.decode_batch(mel_output)
    
# Save the generated audio to a file
torchaudio.save('output.wav', waveforms.squeeze(1), 22050)

print("Audio saved as output.wav")